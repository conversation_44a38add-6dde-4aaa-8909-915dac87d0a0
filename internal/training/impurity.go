// Package training provides core decision tree training functionality.
//
// This package implements the C4.5 algorithm components including:
// - Entropy-based impurity calculations
// - Split evaluation strategies
// - Stopping criteria evaluation
//
// Design Principles:
// - Simple entropy-only impurity calculation
// - Memory-efficient view-based operations
// - Type-safe split evaluation
package training

import (
	"math"
)

// CalculateEntropy calculates entropy for the given target distribution.
//
// Entropy formula: -Σ(p_i * log2(p_i)) where p_i is probability of class i
// Used as the standard impurity measure in C4.5 decision trees.
//
// Args:
// - distribution: Map of target values to their counts (must sum to totalSamples)
// - totalSamples: Total number of samples (must be > 0)
//
// Returns: Entropy value in range [0.0, log2(k)] where k is number of classes
// Constraints: totalSamples must be > 0, distribution counts must be >= 0
// Performance: O(k) where k is number of unique target values
// Relationships: Core calculation used by all split evaluators
// Side effects: None (pure mathematical calculation)
//
// Special cases:
// - Pure node (one class): returns 0.0
// - Empty distribution: returns 0.0
// - Equal distribution: returns log2(k)
//
// Example:
//
//	// Pure node
//	entropy := CalculateEntropy(map[string]int{"yes": 6}, 6) // Returns 0.0
//
//	// Mixed node
//	entropy := CalculateEntropy(map[string]int{"yes": 4, "no": 2}, 6) // Returns ~0.918
func CalculateEntropy[T comparable](distribution map[T]int, totalSamples int) float64 {
	if totalSamples <= 0 {
		return 0.0
	}

	entropy := 0.0
	for _, count := range distribution {
		if count > 0 {
			probability := float64(count) / float64(totalSamples)
			entropy -= probability * math.Log2(probability)
		}
	}

	return entropy
}

// CalculateSplitInformation calculates split information for gain ratio computation.
//
// Split Information formula: -Σ(p_i * log2(p_i)) where p_i is proportion of samples in subset i
// Used to normalize information gain in C4.5 algorithm to prevent bias toward features with many values.
//
// Args:
// - subsetSizes: Slice of subset sizes (must all be >= 0)
// - totalSamples: Total number of samples (must be > 0)
//
// Returns: Split information value in range [0.0, log2(k)] where k is number of subsets
// Constraints: totalSamples must be > 0, all subset sizes must be >= 0
// Performance: O(k) where k is number of subsets
// Relationships: Used by CalculateGainRatio for C4.5 algorithm
// Side effects: None (pure mathematical calculation)
//
// Special cases:
// - Single subset: returns 0.0
// - Empty subsets are ignored
// - Equal subset sizes: returns log2(k)
//
// Example:
//
//	// Binary split with equal subsets
//	splitInfo := CalculateSplitInformation([]int{50, 50}, 100) // Returns 1.0
//
//	// Unbalanced split
//	splitInfo := CalculateSplitInformation([]int{80, 20}, 100) // Returns ~0.722
func CalculateSplitInformation(subsetSizes []int, totalSamples int) float64 {
	if totalSamples <= 0 || len(subsetSizes) <= 1 {
		return 0.0
	}

	splitInfo := 0.0
	for _, size := range subsetSizes {
		if size > 0 {
			proportion := float64(size) / float64(totalSamples)
			splitInfo -= proportion * math.Log2(proportion)
		}
	}

	return splitInfo
}

// CalculateGainRatio calculates gain ratio for the C4.5 algorithm.
//
// Gain Ratio formula: Information Gain / Split Information
// This prevents bias toward features with many possible values by normalizing
// information gain with split information.
//
// Args:
// - informationGain: Information gain from the split (must be >= 0)
// - splitInformation: Split information from the split (must be >= 0)
//
// Returns: Gain ratio value in range [0.0, +inf), typically [0.0, 1.0]
// Constraints: Both parameters must be >= 0
// Performance: O(1) - simple division
// Relationships: Core metric for C4.5 split selection
// Side effects: None (pure mathematical calculation)
//
// Special cases:
// - Zero split information: returns 0.0 (prevents division by zero)
// - Zero information gain: returns 0.0
// - Perfect split: approaches information gain value
//
// Example:
//
//	// Calculate gain ratio for a split
//	gain := 0.5
//	splitInfo := 1.0
//	gainRatio := CalculateGainRatio(gain, splitInfo) // Returns 0.5
func CalculateGainRatio(informationGain, splitInformation float64) float64 {
	if splitInformation <= 0.0 || informationGain <= 0.0 {
		return 0.0
	}

	return informationGain / splitInformation
}
