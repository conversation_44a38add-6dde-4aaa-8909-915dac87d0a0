package training

import (
	"math"
	"testing"

	data "github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/data/features"
)

// buildTestDataset creates a small dataset for split evaluation tests
func buildTestDataset() *data.Dataset[string] {
	d := data.NewDataset[string](0)

	// Features
	d.AddIntColumn("age", []int64{25, 30, 35, 40}, []bool{false, false, false, false})
	d.AddStringColumn("education", []string{"college", "high_school", "college", "graduate"}, []bool{false, false, false, false})

	// Targets: 1x "A", 3x "B"
	d.AddTarget("A")
	d.AddTarget("B")
	d.AddTarget("B")
	d.AddTarget("B")

	return d
}

func TestEvaluateFeatureSplits_Numerical(t *testing.T) {
	d := buildTestDataset()
	view := d.<PERSON>reate<PERSON>iew([]int{0, 1, 2, 3})

	res := EvaluateFeatureSplits(view, d, "age")
	if res == nil {
		t.Fatalf("expected non-nil result")
	}

	if res.FeatureName != "age" || res.FeatureType != features.IntegerFeature {
		t.Fatalf("unexpected feature metadata: %s %v", res.FeatureName, res.FeatureType)
	}

	// Base impurity for targets [A,B,B,B]
	if math.Abs(res.BaseImpurity-0.8112781244591328) > 1e-9 {
		t.Errorf("unexpected base impurity: %v", res.BaseImpurity)
	}

	if res.BestCandidate == nil {
		t.Fatalf("expected a best candidate for age")
	}

	if res.BestCandidate.Type != NumericalSplit {
		t.Fatalf("expected numerical split, got %v", res.BestCandidate.Type)
	}

	if res.BestCandidate.Threshold == nil || math.Abs(*res.BestCandidate.Threshold-27.5) > 1e-6 {
		t.Errorf("expected threshold ~27.5, got %v", res.BestCandidate.Threshold)
	}

	if res.BestCandidate.LeftSize != 1 || res.BestCandidate.RightSize != 3 {
		t.Errorf("unexpected child sizes: L=%d R=%d", res.BestCandidate.LeftSize, res.BestCandidate.RightSize)
	}

	if res.BestCandidate.GainRatio <= 0 {
		t.Errorf("expected positive gain ratio, got %v", res.BestCandidate.GainRatio)
	}
}

func TestEvaluateFeatureSplits_Categorical(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	res := EvaluateFeatureSplits(view, d, "education")
	if res == nil {
		t.Fatalf("expected non-nil result")
	}

	if res.FeatureType != features.StringFeature {
		t.Fatalf("expected string feature type, got %v", res.FeatureType)
	}

	if res.BestCandidate == nil || res.BestCandidate.Type != CategoricalSplit {
		t.Fatalf("expected categorical best candidate")
	}

	// Expect split on "college" vs others
	if res.BestCandidate.Value != "college" {
		t.Errorf("expected best value 'college', got %v", res.BestCandidate.Value)
	}

	if res.BestCandidate.LeftSize != 2 || res.BestCandidate.RightSize != 2 {
		t.Errorf("unexpected child sizes: %d/%d", res.BestCandidate.LeftSize, res.BestCandidate.RightSize)
	}

	if res.BestCandidate.GainRatio <= 0 {
		t.Errorf("expected positive gain ratio, got %v", res.BestCandidate.GainRatio)
	}
}

func TestEvaluateAllFeatureSplits_SelectsBest(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	// Include one missing feature to exercise skip path
	res := EvaluateAllFeatureSplits(view, d, []string{"missing", "age", "education"})
	if res == nil || res.BestCandidate == nil {
		t.Fatalf("expected a best overall result")
	}

	// Age split should have higher gain than education for this dataset
	if res.FeatureName != "age" {
		t.Errorf("expected best feature 'age', got %s", res.FeatureName)
	}
}

func TestApplySplitToView_Numerical(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	res := EvaluateFeatureSplits(view, d, "age")
	if res == nil || res.BestCandidate == nil {
		t.Fatalf("need a split candidate")
	}

	left, right := ApplySplitToView(view, d, res.BestCandidate)
	if left == nil || right == nil {
		t.Fatalf("expected non-nil child views")
	}

	if left.GetSize() != res.BestCandidate.LeftSize || right.GetSize() != res.BestCandidate.RightSize {
		t.Errorf("child sizes mismatch: got %d/%d want %d/%d", left.GetSize(), right.GetSize(), res.BestCandidate.LeftSize, res.BestCandidate.RightSize)
	}
}

func TestApplySplitToView_Categorical(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	res := EvaluateFeatureSplits(view, d, "education")
	if res == nil || res.BestCandidate == nil {
		t.Fatalf("need a split candidate")
	}

	left, right := ApplySplitToView(view, d, res.BestCandidate)
	if left == nil || right == nil {
		t.Fatalf("expected non-nil child views")
	}

	if left.GetSize() != 2 || right.GetSize() != 2 {
		t.Errorf("unexpected sizes: %d/%d", left.GetSize(), right.GetSize())
	}
}

func TestGetSplitDescriptionAndExtractHelpers(t *testing.T) {
	thr := 10.0
	num := &SplitCandidate{FeatureName: "age", Type: NumericalSplit, Threshold: &thr}
	if desc := GetSplitDescription(num); desc != "age <= 10.000" {
		t.Errorf("unexpected numerical desc: %s", desc)
	}

	cat := &SplitCandidate{FeatureName: "education", Type: CategoricalSplit, Value: "college"}
	if desc := GetSplitDescription(cat); desc != "education == 'college'" {
		t.Errorf("unexpected categorical desc: %s", desc)
	}

	if desc := GetSplitDescription(nil); desc != "no split" {
		t.Errorf("unexpected nil desc: %s", desc)
	}

	// extract helpers
	i := int64(7)
	if v := extractNumericalValue(&i); v == nil || *v != 7.0 {
		t.Errorf("extractNumericalValue int64 failed: %v", v)
	}
	f := 3.25
	if v := extractNumericalValue(&f); v == nil || *v != 3.25 {
		t.Errorf("extractNumericalValue float64 failed: %v", v)
	}
	if v := extractNumericalValue("nope"); v != nil {
		t.Errorf("extractNumericalValue should be nil for non-numeric, got %v", v)
	}

	s := "hi"
	if v := extractStringValue(&s); v == nil || *v != "hi" {
		t.Errorf("extractStringValue failed: %v", v)
	}
	if v := extractStringValue(&i); v != nil {
		t.Errorf("extractStringValue should be nil for non-string, got %v", v)
	}
}

func TestEvaluateFeatureSplits_InvalidFeature(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	if res := EvaluateFeatureSplits(view, d, "does_not_exist"); res != nil {
		t.Errorf("expected nil result for missing feature, got %+v", res)
	}
}

func TestEvaluateAllFeatureSplits_NoneValid(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	if res := EvaluateAllFeatureSplits(view, d, []string{"missing1", "missing2"}); res != nil {
		t.Errorf("expected nil when no features are valid, got %+v", res)
	}
}
